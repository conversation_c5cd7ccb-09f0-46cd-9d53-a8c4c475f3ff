<mxfile host="app.diagrams.net" modified="2023-07-29T10:02:18.397Z" agent="Mozilla/5.0" version="21.6.6" etag="some_etag">
  <diagram id="neural_network_architecture" name="Neural Network Architecture">
    <mxGraphModel dx="1122" dy="660" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Top Diagram (C3k2,c3k=True) -->
        <mxCell id="top_container" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;strokeWidth=1;fillColor=none;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="100" y="80" width="650" height="120" as="geometry" />
        </mxCell>
        
        <!-- Top Diagram Components -->
        <mxCell id="conv1_top" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="180" y="120" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="split_top" value="Split" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="320" y="120" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="c3k_top" value="C3k" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#80FFFF;strokeColor=#006666;" vertex="1" parent="1">
          <mxGeometry x="440" y="90" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="c_node_top" value="C" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="540" y="120" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv2_top" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="610" y="120" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- Top Labels -->
        <mxCell id="xn_top" value="×n" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="500" y="140" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="x_in_top" value="x&lt;sub&gt;in&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="140" y="120" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="x_out_top" value="x&lt;sub&gt;out&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="120" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label_top" value="C3k2,c3k=True" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="610" y="90" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Top Connections -->
        <mxCell id="edge1_top" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="x_in_top" target="conv1_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="150" y="140" as="sourcePoint" />
            <mxPoint x="200" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge2_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv1_top" target="split_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="140" as="sourcePoint" />
            <mxPoint x="340" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge3_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="split_top" target="c_node_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="140" as="sourcePoint" />
            <mxPoint x="460" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge4_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="c_node_top" target="conv2_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="140" as="sourcePoint" />
            <mxPoint x="630" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge5_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv2_top" target="x_out_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="140" as="sourcePoint" />
            <mxPoint x="750" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="branch_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="split_top" target="c3k_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="130" as="sourcePoint" />
            <mxPoint x="440" y="110" as="targetPoint" />
            <Array as="points">
              <mxPoint x="420" y="130" />
              <mxPoint x="420" y="110" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="return_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k_top" target="c_node_top">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="110" as="sourcePoint" />
            <mxPoint x="555" y="120" as="targetPoint" />
            <Array as="points">
              <mxPoint x="555" y="110" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Bottom Diagram (C3k2,c3k=False) -->
        <mxCell id="bottom_container" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;strokeWidth=1;fillColor=none;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="100" y="240" width="650" height="120" as="geometry" />
        </mxCell>
        
        <!-- Bottom Diagram Components -->
        <mxCell id="conv1_bottom" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="180" y="280" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="split_bottom" value="Split" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="320" y="280" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="bottleneck_bottom" value="BottleNeck" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#00997A;strokeColor=#006666;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="440" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="c_node_bottom" value="C" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="540" y="280" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv2_bottom" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" vertex="1" parent="1">
          <mxGeometry x="610" y="280" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- Bottom Labels -->
        <mxCell id="xn_bottom" value="×n" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="500" y="300" width="20" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="x_in_bottom" value="x&lt;sub&gt;in&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="140" y="280" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="x_out_bottom" value="x&lt;sub&gt;out&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="720" y="280" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="label_bottom" value="C3k2,c3k=False" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="610" y="250" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Bottom Connections -->
        <mxCell id="edge1_bottom" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="x_in_bottom" target="conv1_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="150" y="300" as="sourcePoint" />
            <mxPoint x="200" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge2_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv1_bottom" target="split_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="290" y="300" as="sourcePoint" />
            <mxPoint x="340" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge3_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="split_bottom" target="c_node_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="300" as="sourcePoint" />
            <mxPoint x="460" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge4_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="c_node_bottom" target="conv2_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="300" as="sourcePoint" />
            <mxPoint x="630" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge5_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv2_bottom" target="x_out_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="branch_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="split_bottom" target="bottleneck_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="290" as="sourcePoint" />
            <mxPoint x="440" y="270" as="targetPoint" />
            <Array as="points">
              <mxPoint x="420" y="290" />
              <mxPoint x="420" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="return_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="bottleneck_bottom" target="c_node_bottom">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="270" as="sourcePoint" />
            <mxPoint x="555" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="555" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Bottom Source Reference -->
        <mxCell id="source_ref" value="CSDN@张三得了鱼" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#999999;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="610" y="320" width="100" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
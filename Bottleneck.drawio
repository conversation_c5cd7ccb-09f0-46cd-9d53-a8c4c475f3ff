<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="bottleneck_bool_diagram" name="Bottleneck Bool">
    <mxGraphModel dx="1596" dy="825" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="container" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#000000;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="110" y="20" width="280" height="220" as="geometry" />
        </mxCell>
        <mxCell id="conv1" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="140" y="80" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv2" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="140" y="140" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv3" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="300" y="80" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="conv4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="conv4" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="300" y="140" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="conv1" target="conv2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="120" as="sourcePoint" />
            <mxPoint x="230" y="70" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="179.5" y="170" as="sourcePoint" />
            <mxPoint x="180" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="conv3" target="conv4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="190" as="sourcePoint" />
            <mxPoint x="180" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="skip3" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="180" as="sourcePoint" />
            <mxPoint x="180" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="label" value="Bottleneck&#xa;Bool" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="210" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="conv3">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="40" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-10" value="Add=True" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="105" y="20" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="179.51" y="40" as="sourcePoint" />
            <mxPoint x="179.51" y="80" as="targetPoint" />
            <Array as="points">
              <mxPoint x="179.51" y="61" />
              <mxPoint x="180.51" y="61" />
              <mxPoint x="180.51" y="60" />
              <mxPoint x="179.51" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-15" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="50" as="sourcePoint" />
            <mxPoint x="130" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-16" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="50" as="sourcePoint" />
            <mxPoint x="180" y="50" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0YKqc8E4-J9Wgu2BuBfG-17" value="Add=Flase" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="270" y="30" width="80" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

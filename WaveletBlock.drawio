<mxfile host="app.diagrams.net" modified="2023-10-30T12:00:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="21.6.6" type="device">
  <diagram name="第 1 页" id="page-1">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 整体背景框 -->
        <mxCell id="2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="160" y="40" width="320" height="780" as="geometry" />
        </mxCell>
        
        <!-- 顶部模块 - WaveletPooling Block -->
        <mxCell id="3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="180" y="60" width="280" height="120" as="geometry" />
        </mxCell>
        
        <!-- Conv 1x1 K=λ, p=1 模块 -->
        <mxCell id="40" value="Conv 1x1&lt;br&gt;K=λ, p=1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="340" y="100" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- WaveletPooling 模块 -->
        <mxCell id="5" value="WaveletPooling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="220" y="100" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- WaveletPooling Block 标签 -->
        <mxCell id="6" value="WaveletPooling Block" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;rotation=-90;" vertex="1" parent="1">
          <mxGeometry x="160" y="105" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- 顶部模块之间的箭头 -->
        <mxCell id="7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="40" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 顶部输入箭头 -->
        <mxCell id="39" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="40">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="120" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 中间模块 - WaveletPooling Repconv Block -->
        <mxCell id="8" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#000000;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="220" width="280" height="200" as="geometry" />
        </mxCell>
        
        <!-- 中间模块内部组件 -->
        <mxCell id="9" value="Conv 1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="240" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="10" value="Repconv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="300" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="11" value="WaveletPooling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="360" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="12" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 第二个Conv 1x1 -->
        <mxCell id="14" value="Conv 1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="420" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 加号圆圈 -->
        <mxCell id="16" value="+" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="305" y="480" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="17" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="14" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 左侧连接到加号 -->
        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="320" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="200" y="495" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- WaveletPooling Repconv Block 标签 -->
        <mxCell id="19" value="WaveletPooling Repconv Block" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;rotation=-90;" vertex="1" parent="1">
          <mxGeometry x="160" y="305" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- 下部模块 - 与中间模块类似 -->
        <mxCell id="20" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#000000;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="180" y="520" width="280" height="160" as="geometry" />
        </mxCell>
        
        <!-- 下部模块内部组件 -->
        <mxCell id="21" value="Conv 1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="540" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="22" value="Repconv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="600" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="16" target="21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="21" target="22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 第三个Conv 1x1 -->
        <mxCell id="25" value="Conv 1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="270" y="660" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="26" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 第二个加号圆圈 -->
        <mxCell id="27" value="+" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="305" y="720" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="28" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="25" target="27">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 左侧连接到第二个加号 -->
        <mxCell id="29" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="27">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="600" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="200" y="735" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 底部模块 - Details -->
        <mxCell id="30" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="180" y="760" width="280" height="80" as="geometry" />
        </mxCell>
        
        <!-- Details 内部组件 -->
        <mxCell id="31" value="DRCB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="220" y="780" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="32" value="C" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="350" y="785" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- Details 标签 -->
        <mxCell id="33" value="Details" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;rotation=-90;" vertex="1" parent="1">
          <mxGeometry x="160" y="785" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- 连接到底部模块 -->
        <mxCell id="34" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="27" target="32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="320" y="760" />
              <mxPoint x="365" y="760" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 右侧输入到C -->
        <mxCell id="35" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="32">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="800" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- C到DRCB的连接 -->
        <mxCell id="36" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="32" target="31">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- DRCB到输出的连接 -->
        <mxCell id="37" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="31">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="160" y="800" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 顶部到中间的连接 -->
        <mxCell id="38" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 添加顶部标签 -->
        <mxCell id="41" value="Conv 1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="220" y="70" width="100" height="30" as="geometry" />
        </mxCell>
        
        <!-- 顶部箭头 -->
        <mxCell id="42" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="41" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
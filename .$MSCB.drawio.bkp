<mxfile host="app.diagrams.net" modified="2023-05-10T12:00:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="123456789" version="21.0.6" type="device">
  <diagram id="MSCB_diagram" name="MSCB">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 外部虚线框 -->
        <mxCell id="2" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;strokeWidth=2;fillColor=none;strokeColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="120" y="120" width="600" height="160" as="geometry" />
        </mxCell>
        <!-- MSCB 标签 -->
        <mxCell id="3" value="(e) MSCB" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="380" y="280" width="80" height="30" as="geometry" />
        </mxCell>
        <!-- Conv1x1 C/2 模块 -->
        <mxCell id="4" value="Conv1x1 C/2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontSize=12;verticalAlign=middle;align=center;" parent="1" vertex="1">
          <mxGeometry x="160" y="160" width="70" height="80" as="geometry" />
        </mxCell>
        <!-- BN 模块1 -->
        <mxCell id="5" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;verticalAlign=middle;align=center;" parent="1" vertex="1">
          <mxGeometry x="260" y="160" width="50" height="80" as="geometry" />
        </mxCell>
        <!-- MSDC 模块 -->
        <mxCell id="6" value="MSDC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;verticalAlign=middle;align=center;" parent="1" vertex="1">
          <mxGeometry x="340" y="160" width="60" height="80" as="geometry" />
        </mxCell>
        <!-- Conv1x1 模块 -->
        <mxCell id="7" value="Conv1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontSize=12;verticalAlign=middle;align=center;" parent="1" vertex="1">
          <mxGeometry x="430" y="160" width="70" height="80" as="geometry" />
        </mxCell>
        <!-- BN 模块2 -->
        <mxCell id="8" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;verticalAlign=middle;align=center;" parent="1" vertex="1">
          <mxGeometry x="530" y="160" width="50" height="80" as="geometry" />
        </mxCell>
        <!-- 输入点 -->
        <mxCell id="9" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#000000;strokeColor=none;fontSize=1;" parent="1" vertex="1">
          <mxGeometry x="120" y="195" width="10" height="10" as="geometry" />
        </mxCell>
        <!-- 输出点 -->
        <mxCell id="10" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#000000;strokeColor=none;fontSize=1;" parent="1" vertex="1">
          <mxGeometry x="620" y="195" width="10" height="10" as="geometry" />
        </mxCell>
        <!-- 分支点 -->
        <mxCell id="11" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#000000;strokeColor=none;fontSize=1;" parent="1" vertex="1">
          <mxGeometry x="650" y="195" width="10" height="10" as="geometry" />
        </mxCell>
        <!-- 输入到第一个Conv的连接 -->
        <mxCell id="12" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="9" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="200" as="sourcePoint" />
            <mxPoint x="160" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Conv到BN的连接 -->
        <mxCell id="13" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="4" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="200" as="sourcePoint" />
            <mxPoint x="260" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- BN到MSDC的连接 -->
        <mxCell id="14" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="5" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="200" as="sourcePoint" />
            <mxPoint x="340" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- MSDC到Conv的连接 -->
        <mxCell id="15" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="6" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="200" as="sourcePoint" />
            <mxPoint x="430" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Conv到BN的连接 -->
        <mxCell id="16" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="7" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="530" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- BN到输出点的连接 -->
        <mxCell id="17" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="8" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="200" as="sourcePoint" />
            <mxPoint x="610" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 输出到分支点的连接 -->
        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="10" target="11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="630" y="200" as="sourcePoint" />
            <mxPoint x="650" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 绕过的虚线连接 -->
        <mxCell id="19" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;dashed=1;dashPattern=8 8;edgeStyle=orthogonalEdgeStyle;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="125" y="180" as="sourcePoint" />
            <mxPoint x="655" y="180" as="targetPoint" />
            <Array as="points">
              <mxPoint x="125" y="120" />
              <mxPoint x="655" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
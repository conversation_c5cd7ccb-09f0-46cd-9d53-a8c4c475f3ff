<mxfile host="app.diagrams.net" modified="2023-08-01T12:00:00.000Z" agent="Mozilla/5.0" etag="your-etag" version="21.6.6">
  <diagram id="bottleneck_bool_diagram" name="Bottleneck Bool">
    <mxGraphModel dx="800" dy="500" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Main container box -->
        <mxCell id="container" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#000000;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="40" width="180" height="300" as="geometry" />
        </mxCell>
        
        <!-- Inner container box for the first part -->
        <mxCell id="inner_container" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#000000;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="60" width="140" height="130" as="geometry" />
        </mxCell>
        
        <!-- Conv blocks -->
        <mxCell id="conv1" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="140" y="80" width="80" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv2" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="140" y="140" width="80" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv3" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="140" y="220" width="80" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv4" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFB000;strokeColor=#BD7000;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="140" y="280" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv1" target="conv2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="120" as="sourcePoint" />
            <mxPoint x="230" y="70" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="179.5" y="170" as="sourcePoint" />
            <mxPoint x="180" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv3" target="conv4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="260" as="sourcePoint" />
            <mxPoint x="230" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Skip connection -->
        <mxCell id="skip1" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="conv1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="95" as="sourcePoint" />
            <mxPoint x="180" y="45" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="skip2" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="95" as="sourcePoint" />
            <mxPoint x="130" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="skip3" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="180" as="sourcePoint" />
            <mxPoint x="180" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Label -->
        <mxCell id="label" value="Bottleneck&#xa;Bool" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="15" y="330" width="100" height="40" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
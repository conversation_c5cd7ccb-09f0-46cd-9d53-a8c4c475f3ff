<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="Mozilla/5.0" version="21.1.1" type="device">
<diagram id="network-diagram" name="Network Architecture">
<mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
<root>
<mxCell id="0"/>
<mxCell id="1" parent="0"/>

<!-- Backbone Section -->
<mxCell id="backbone_label" value="Backbone" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" vertex="1" parent="1">
    <mxGeometry x="40" y="20" width="100" height="30" as="geometry"/>
</mxCell>

<!-- CBS 0 -->
<mxCell id="cbs0" value="CBS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
    <mxGeometry x="40" y="600" width="120" height="40" as="geometry"/>
</mxCell>

<!-- CBS 1 -->
<mxCell id="cbs1" value="CBS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
    <mxGeometry x="40" y="520" width="120" height="40" as="geometry"/>
</mxCell>

<!-- C3K2 2 -->
<mxCell id="c3k2_2" value="C3K2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
    <mxGeometry x="40" y="440" width="120" height="40" as="geometry"/>
</mxCell>

<!-- CBS 3 -->
<mxCell id="cbs3" value="CBS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
    <mxGeometry x="40" y="360" width="120" height="40" as="geometry"/>
</mxCell>

<!-- C3K2 4 -->
<mxCell id="c3k2_4" value="C3K2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
    <mxGeometry x="40" y="280" width="120" height="40" as="geometry"/>
</mxCell>

<!-- CBS 5 -->
<mxCell id="cbs5" value="CBS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
    <mxGeometry x="40" y="200" width="120" height="40" as="geometry"/>
</mxCell>

<!-- C3K2 6 -->
<mxCell id="c3k2_6" value="C3K2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
    <mxGeometry x="40" y="120" width="120" height="40" as="geometry"/>
</mxCell>

<!-- CBS 7 -->
<mxCell id="cbs7" value="CBS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
    <mxGeometry x="40" y="40" width="120" height="40" as="geometry"/>
</mxCell>

<!-- C3K2 8 -->
<mxCell id="c3k2_8" value="C3K2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
    <mxGeometry x="40" y="-40" width="120" height="40" as="geometry"/>
</mxCell>

<!-- SPPF 9 -->
<mxCell id="sppf9" value="SPPF" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
    <mxGeometry x="40" y="-120" width="120" height="40" as="geometry"/>
</mxCell>

<!-- C2PSA 10 -->
<mxCell id="c2psa10" value="C2PSA" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
    <mxGeometry x="40" y="-200" width="120" height="40" as="geometry"/>
</mxCell>

<!-- 连接线和标注 -->
<mxCell id="edge1" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cbs0" target="cbs1">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>
<mxCell id="label1" value="16x320x320" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
    <mxGeometry x="120" y="570" width="80" height="20" as="geometry"/>
</mxCell>

<mxCell id="edge2" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cbs1" target="c3k2_2">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>
<mxCell id="label2" value="32x160x160" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
    <mxGeometry x="120" y="490" width="90" height="20" as="geometry"/>
</mxCell>

<mxCell id="edge3" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_2" target="cbs3">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>
<mxCell id="label3" value="64x80x80" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
    <mxGeometry x="120" y="410" width="80" height="20" as="geometry"/>
</mxCell>

<mxCell id="edge4" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cbs3" target="c3k2_4">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>
<mxCell id="label4" value="128x40x40" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
    <mxGeometry x="120" y="330" width="90" height="20" as="geometry"/>
</mxCell>

<mxCell id="edge5" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_4" target="cbs5">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>
<mxCell id="label5" value="256x20x20" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" vertex="1" parent="1">
    <mxGeometry x="120" y="250" width="90" height="20" as="geometry"/>
</mxCell>

<mxCell id="edge6" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cbs5" target="c3k2_6">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="edge7" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_6" target="cbs7">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="edge8" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="cbs7" target="c3k2_8">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="edge9" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_8" target="sppf9">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="edge10" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="sppf9" target="c2psa10">
    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<!-- Backbone 外框 -->
<mxCell id="backbone_container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=none;dashed=1;" vertex="1" parent="1">
    <mxGeometry x="20" y="-220" width="160" height="880" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile>
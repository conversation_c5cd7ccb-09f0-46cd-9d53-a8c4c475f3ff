<mxfile host="app.diagrams.net" modified="2024-05-21T08:30:12.789Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="qV5z3d6-H-VfB6u0M8rZ" version="24.4.2" type="device">
  <diagram name="Page-1" id="T1B2_uS74vjT84Hh_w2f">
    <mxGraphModel dx="2074" dy="1141" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Main Path Start -->
        <mxCell id="input_feature" value="输入特征图 x[i]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="40" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conv_gn_1" value="Conv_GN 3×3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="230" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        <!-- Shared Convolution Group -->
        <mxCell id="group_shared_conv" value="共享卷积 (self.share_conv)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="420" y="200" width="360" height="140" as="geometry" />
        </mxCell>
        <mxCell id="conv_gn_2" value="Conv_GN 3×3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="group_shared_conv">
          <mxGeometry x="20" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="conv_gn_3" value="Conv_GN 1×1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="group_shared_conv">
          <mxGeometry x="200" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        <!-- Shared Feature Splitter -->
        <mxCell id="shared_feature" value="共享特征" style="rhombus;whiteSpace=wrap;html=1;rounded=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="230" width="120" height="80" as="geometry" />
        </mxCell>
        <!-- Regression Branch -->
        <mxCell id="group_reg_branch" value="回归分支 (Regression Branch)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="1000" y="40" width="180" height="230" as="geometry" />
        </mxCell>
        <mxCell id="conv2d_cv2" value="Conv2d 1×1&lt;br&gt;(self.cv2)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1;" vertex="1" parent="group_reg_branch">
          <mxGeometry x="20" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="scale_op" value="Scale" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="group_reg_branch">
          <mxGeometry x="20" y="140" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="pred_corners" value="&lt;b&gt;pred_corners&lt;/b&gt;&lt;br&gt;预测框分布" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1230" y="105" width="140" height="60" as="geometry" />
        </mxCell>
        <!-- Classification Branch -->
        <mxCell id="group_cls_branch" value="分类分支 (Classification Branch)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="1000" y="350" width="180" height="170" as="geometry" />
        </mxCell>
        <mxCell id="conv2d_cv3" value="Conv2d 1×1&lt;br&gt;(self.cv3)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="group_cls_branch">
          <mxGeometry x="20" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="initial_scores" value="初始分类得分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="group_cls_branch">
          <mxGeometry x="20" y="120" width="140" height="40" as="geometry" />
        </mxCell>
        <!-- LQE Module -->
        <mxCell id="group_lqe" value="位置质量估计 (LQE Module)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="1200" y="200" width="410" height="200" as="geometry" />
        </mxCell>
        <mxCell id="lqe_stats" value="统计分析&lt;br&gt;(top-k, mean)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="group_lqe">
          <mxGeometry x="20" y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="lqe_mlp" value="MLP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="group_lqe">
          <mxGeometry x="150" y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="lqe_adjust" value="质量分调整值" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="group_lqe">
          <mxGeometry x="280" y="50" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="lqe_add" value="+" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#b85450;strokeWidth=2;fontSize=24;fontStyle=1;fillColor=#f8cecc;" vertex="1" parent="group_lqe">
          <mxGeometry x="185" y="130" width="50" height="50" as="geometry" />
        </mxCell>
        <!-- Final Output Path -->
        <mxCell id="pred_scores" value="&lt;b&gt;pred_scores&lt;/b&gt;&lt;br&gt;融合后得分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1415" y="420" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="concat" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1650" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="output" value="输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1820" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- Connectors -->
        <mxCell id="arrow-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="input_feature" target="conv_gn_1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="conv_gn_1" target="conv_gn_2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="group_shared_conv" source="conv_gn_2" target="conv_gn_3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="conv_gn_3" target="shared_feature">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;entryX=0;entryY=0.5;" edge="1" parent="1" source="shared_feature" target="conv2d_cv2">
          <mxGeometry relative="1" as="geometry">
            <mxPoint as="sourcePoint" x="890" y="230" />
            <mxPoint as="targetPoint" x="1000" y="135" />
            <Array as="points">
              <mxPoint x="890" y="135" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;entryX=0;entryY=0.5;" edge="1" parent="1" source="shared_feature" target="conv2d_cv3">
          <mxGeometry relative="1" as="geometry">
            <mxPoint as="sourcePoint" x="890" y="310" />
            <mxPoint as="targetPoint" x="1000" y="435" />
            <Array as="points">
              <mxPoint x="890" y="435" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="group_reg_branch" source="conv2d_cv2" target="scale_op">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="scale_op" target="pred_corners">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="pred_corners" target="lqe_stats">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="group_lqe" source="lqe_stats" target="lqe_mlp">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="group_lqe" source="lqe_mlp" target="lqe_adjust">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="group_lqe" source="lqe_adjust" target="lqe_add">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;" edge="1" parent="group_cls_branch" source="conv2d_cv3" target="initial_scores">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;entryX=0;entryY=0.5;" edge="1" parent="1" source="initial_scores" target="lqe_add">
          <mxGeometry relative="1" as="geometry">
            <mxPoint as="sourcePoint" x="1090" y="500" />
            <mxPoint as="targetPoint" x="1400" y="355" />
            <Array as="points">
              <mxPoint x="1090" y="540" />
              <mxPoint x="1270" y="540" />
              <mxPoint x="1270" y="355" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;entryX=0;entryY=0.5;" edge="1" parent="1" source="lqe_add" target="pred_scores">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1425" y="400" />
              <mxPoint x="1485" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.25;" edge="1" parent="1" source="pred_corners" target="concat">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1600" y="135" />
              <mxPoint x="1600" y="255" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.75;" edge="1" parent="1" source="pred_scores" target="concat">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1600" y="450" />
              <mxPoint x="1600" y="285" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="concat" target="output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

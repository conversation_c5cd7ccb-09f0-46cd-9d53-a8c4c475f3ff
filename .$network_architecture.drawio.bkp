<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-06-23T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="Page-1" id="xxx">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Input Image -->
        <mxCell id="input" value="" style="shape=image;html=1;verticalAlign=top;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;imageAspect=1;aspect=fixed;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" vertex="1" parent="1">
          <mxGeometry x="200" y="40" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="inputLabel" value="Input" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="55" width="40" height="30" as="geometry" />
        </mxCell>
        
        <!-- Backbone Section -->
        <mxCell id="backboneLabel" value="Backbone" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="30" y="30" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- Backbone Conv blocks -->
        <mxCell id="conv1" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv2" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="170" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="fcm1" value="FCM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="60" y="220" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv3" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="270" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="fcm2" value="FCM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="60" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv4" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="370" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="fcm3" value="FCM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="60" y="420" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="mkp" value="MKP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="60" y="470" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="fcm4" value="FCM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="60" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sppf" value="SPPF" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="60" y="570" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- Neck Section -->
        <mxCell id="neckLabel" value="Neck" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="130" width="50" height="30" as="geometry" />
        </mxCell>
        
        <!-- Neck components -->
        <mxCell id="c2f1" value="C2f" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="200" y="170" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="concat1" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="280" y="220" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="up1" value="UP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="200" y="270" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="c2f2" value="C2f" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="200" y="320" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="concat2" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="280" y="370" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="up2" value="UP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="200" y="420" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv5" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="380" y="220" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="concat3" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="280" y="470" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="c2f3" value="C2f" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="380" y="420" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv6" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="380" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="concat4" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="480" y="570" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="c2f4" value="C2f" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="480" y="620" width="50" height="30" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <!-- Input to first Conv -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="input" target="conv1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="80" as="sourcePoint" />
            <mxPoint x="90" y="110" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Backbone vertical connections -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv1" target="conv2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="150" as="sourcePoint" />
            <mxPoint x="90" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv2" target="fcm1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="200" as="sourcePoint" />
            <mxPoint x="90" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm1" target="conv3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="250" as="sourcePoint" />
            <mxPoint x="90" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv3" target="fcm2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="300" as="sourcePoint" />
            <mxPoint x="90" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm2" target="conv4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="350" as="sourcePoint" />
            <mxPoint x="90" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv4" target="fcm3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="400" as="sourcePoint" />
            <mxPoint x="90" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow8" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm3" target="mkp">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="450" as="sourcePoint" />
            <mxPoint x="90" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow9" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="mkp" target="fcm4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="500" as="sourcePoint" />
            <mxPoint x="90" y="520" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm4" target="sppf">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="90" y="550" as="sourcePoint" />
            <mxPoint x="90" y="570" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Backbone to Neck connections -->
        <mxCell id="arrow11" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm1" target="concat1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="235" as="sourcePoint" />
            <mxPoint x="280" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow12" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm2" target="concat2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="335" as="sourcePoint" />
            <mxPoint x="280" y="385" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow13" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="fcm3" target="concat3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="435" as="sourcePoint" />
            <mxPoint x="280" y="485" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow14" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="sppf" target="concat4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="585" as="sourcePoint" />
            <mxPoint x="510" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Neck internal connections -->
        <mxCell id="arrow15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="c2f1" target="up1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="225" y="200" as="sourcePoint" />
            <mxPoint x="225" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="up1" target="c2f2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="225" y="300" as="sourcePoint" />
            <mxPoint x="225" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow17" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="c2f2" target="up2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="225" y="350" as="sourcePoint" />
            <mxPoint x="225" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow18" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="concat1" target="conv5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="235" as="sourcePoint" />
            <mxPoint x="380" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow19" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="concat2" target="c2f3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="385" as="sourcePoint" />
            <mxPoint x="380" y="435" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="c2f3" target="conv6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="405" y="450" as="sourcePoint" />
            <mxPoint x="405" y="520" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow21" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv6" target="concat4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="535" as="sourcePoint" />
            <mxPoint x="480" y="585" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="concat4" target="c2f4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="600" as="sourcePoint" />
            <mxPoint x="505" y="620" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

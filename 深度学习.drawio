<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram name="第 1 页" id="osV70-il0n7cuKCf4UHu">
    <mxGraphModel dx="988" dy="570" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="-YzlKN9j5lAXaHFh2exn-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=12;fontColor=default;" edge="1" parent="1" source="-YzlKN9j5lAXaHFh2exn-1" target="-YzlKN9j5lAXaHFh2exn-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-1" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;Input&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="360" y="200" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=12;fontColor=default;" edge="1" parent="1" source="-YzlKN9j5lAXaHFh2exn-3" target="-YzlKN9j5lAXaHFh2exn-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-3" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;font face=&quot;宋体&quot;&gt;数据预处理&lt;/font&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="345" y="280" width="110" height="40" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;fontSize=12;fontColor=default;" edge="1" parent="1" source="-YzlKN9j5lAXaHFh2exn-4" target="-YzlKN9j5lAXaHFh2exn-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-4" value="&lt;font face=&quot;宋体&quot; style=&quot;font-size: 18px;&quot;&gt;数据增强&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="360" y="360" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontFamily=Times New Roman;fontSize=12;fontColor=default;" edge="1" parent="1" source="-YzlKN9j5lAXaHFh2exn-5" target="-YzlKN9j5lAXaHFh2exn-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-5" value="&lt;span style=&quot;font-size: 18px;&quot;&gt;&lt;font face=&quot;宋体&quot;&gt;模型训练&lt;/font&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="360" y="440" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="-YzlKN9j5lAXaHFh2exn-6" value="&lt;font face=&quot;宋体&quot; style=&quot;font-size: 18px;&quot;&gt;模型评估&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=1;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="360" y="520" width="80" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

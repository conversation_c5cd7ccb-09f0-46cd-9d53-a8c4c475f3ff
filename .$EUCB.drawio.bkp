<mxfile host="app.diagrams.net" modified="2023-11-10T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="15.7.3" type="device">
  <diagram id="EUCB_diagram" name="EUCB">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 外部虚线框 -->
        <mxCell id="2" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;fillColor=none;strokeWidth=2;strokeColor=#99CC99;" vertex="1" parent="1">
          <mxGeometry x="120" y="120" width="560" height="160" as="geometry" />
        </mxCell>
        <!-- Up_2x 模块 -->
        <mxCell id="3" value="Up_2x" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="160" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <!-- DWC3x3 模块 -->
        <mxCell id="4" value="DWC3x3" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="280" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <!-- BN 模块 -->
        <mxCell id="5" value="BN" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="400" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <!-- ReLU 模块 -->
        <mxCell id="6" value="ReLU" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="520" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <!-- Conv1x1 模块 -->
        <mxCell id="7" value="Conv1x1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="640" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <!-- 箭头连接 -->
        <mxCell id="8" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="9" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="10" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="11" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 输入箭头 -->
        <mxCell id="12" value="" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="200" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 输出箭头 -->
        <mxCell id="13" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="760" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- EUCB 标签 -->
        <mxCell id="14" value="(c) EUCB" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="380" y="260" width="80" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
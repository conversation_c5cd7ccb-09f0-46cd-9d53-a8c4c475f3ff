<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram name="Transformer Encoder" id="transformer-encoder">
    <mxGraphModel dx="841" dy="567" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="Transformer Encoder" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="280" y="200" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="container" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#f5f5f5;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="298" y="250" width="170" height="400" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=1;strokeColor=default;" parent="1" source="add_top" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="380" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="add_top" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;+&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#ffffff;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="365" y="260" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="mlp" value="CGLU" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#b3d9ff;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="310" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="norm1" value="Norm" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#fff2cc;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="380" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="jcpazdSSS8wJAgmUcO2N-1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endArrow=none;endFill=0;fontFamily=Times New Roman;fontSize=16;curved=0;strokeWidth=1;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-23" target="norm1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="add_bottom" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;+&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#ffffff;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="365" y="440" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arrow5" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=default;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="norm1" target="mlp" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="370" as="sourcePoint" />
            <mxPoint x="380" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow6" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=default;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="mlp" target="add_top" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="300" as="sourcePoint" />
            <mxPoint x="380" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-5" target="GYRk7u7YPJ6JGuiL0bJS-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-5" target="GYRk7u7YPJ6JGuiL0bJS-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-5" value="" style="shape=partialRectangle;whiteSpace=wrap;html=1;bottom=1;right=1;left=1;top=0;fillColor=none;routingCenterX=-0.5;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;strokeColor=default;" parent="1" vertex="1">
          <mxGeometry x="350" y="550" width="60" height="10" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-9" target="GYRk7u7YPJ6JGuiL0bJS-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-9" value="Norm" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#fff2cc;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="571" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endArrow=none;endFill=0;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-10" target="add_bottom" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-10" value="Multi-Head&#xa;Attention" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#d5e8d4;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="490" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;fontFamily=Times New Roman;fontSize=16;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-15" target="GYRk7u7YPJ6JGuiL0bJS-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-15" value="Input" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=default;fillColor=#f8cecc;fontSize=16;fontStyle=1;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="660" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=1;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-20" target="add_bottom" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="450" y="640" />
              <mxPoint x="450" y="455" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-20" value="" style="shape=waypoint;sketch=0;fillStyle=solid;size=6;pointerEvents=1;points=[];fillColor=none;resizable=0;rotatable=0;perimeter=centerPerimeter;snapToPoint=1;strokeColor=none;fontFamily=Times New Roman;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="370" y="630" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-22" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryDx=0;entryDy=0;endArrow=none;endFill=0;fontFamily=Times New Roman;fontSize=16;curved=0;strokeWidth=1;strokeColor=default;" parent="1" source="add_bottom" target="GYRk7u7YPJ6JGuiL0bJS-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="380" y="440" as="sourcePoint" />
            <mxPoint x="380" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;strokeWidth=1;strokeColor=default;" parent="1" source="GYRk7u7YPJ6JGuiL0bJS-23" target="add_top" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="450" y="430" />
              <mxPoint x="450" y="275" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GYRk7u7YPJ6JGuiL0bJS-23" value="" style="shape=waypoint;sketch=0;fillStyle=solid;size=6;pointerEvents=1;points=[];fillColor=none;resizable=0;rotatable=0;perimeter=centerPerimeter;snapToPoint=1;fontFamily=Times New Roman;strokeWidth=1;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="370" y="420" width="20" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

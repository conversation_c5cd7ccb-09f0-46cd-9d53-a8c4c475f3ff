<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="MSDC_diagram" name="MSDC">
    <mxGraphModel dx="2314" dy="2296" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="3" value="&amp;nbsp;MSDC" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="350" y="420" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="4" value="DWCpxp" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="220" y="140" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="5" value="DWCqxq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="350" y="140" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="6" value="DWCsxs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="480" y="140" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="21" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="220" y="200" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="22" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="350" y="200" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="23" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="480" y="200" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="4" target="21" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="25" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="22" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="26" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="6" target="23" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-33" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="VTT7dj26zq-N4ib9YGN7-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="10" value="ReLU6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="220" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="VTT7dj26zq-N4ib9YGN7-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="11" value="ReLU6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="350" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-34" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="12" target="VTT7dj26zq-N4ib9YGN7-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="12" value="ReLU6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="480" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="27" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="21" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="28" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="22" target="11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="29" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="23" target="12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="16" value="Channel Shuffle" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="300" y="370" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="120" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="120" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="120" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="VTT7dj26zq-N4ib9YGN7-31" target="VTT7dj26zq-N4ib9YGN7-32">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-31" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
          <mxGeometry x="382" y="310" width="36" height="36" as="geometry" />
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-38" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="VTT7dj26zq-N4ib9YGN7-32" target="16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-32" value="+" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="385" y="313" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-39" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="120" as="sourcePoint" />
            <mxPoint x="400" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-40" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="120" as="sourcePoint" />
            <mxPoint x="400" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-41" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="40" as="sourcePoint" />
            <mxPoint x="530" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-42" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="40" as="sourcePoint" />
            <mxPoint x="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VTT7dj26zq-N4ib9YGN7-44" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;dashed=1;strokeColor=#FF00FF;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="200" width="400" height="440" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

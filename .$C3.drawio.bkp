<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2023-11-04T12:00:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="example-etag" version="21.6.6" type="device">
  <diagram name="第 1 页" id="page-1">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 输入 X -->
        <mxCell id="2" value="X" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="360" y="40" width="40" height="20" as="geometry" />
        </mxCell>
        
        <!-- 第一个 Conv 模块 -->
        <mxCell id="3" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="320" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- X 到 Conv 的箭头 -->
        <mxCell id="4" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="2" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="60" as="sourcePoint" />
            <mxPoint x="430" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Split 模块 -->
        <mxCell id="5" value="Split" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="320" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Conv 到 Split 的箭头 -->
        <mxCell id="6" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="5">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 第一个 Bottleneck 模块 -->
        <mxCell id="7" value="Bottleneck" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="320" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Split 到 Bottleneck 的箭头 -->
        <mxCell id="8" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="7">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 第二个 Bottleneck 模块 -->
        <mxCell id="9" value="Bottleneck" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="320" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 第一个到第二个 Bottleneck 的箭头 -->
        <mxCell id="10" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="9">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="530" as="sourcePoint" />
            <mxPoint x="440" y="480" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 第三个 Bottleneck 模块 -->
        <mxCell id="11" value="Bottleneck" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="320" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 第二个到第三个 Bottleneck 的箭头 -->
        <mxCell id="12" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="11">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="630" as="sourcePoint" />
            <mxPoint x="440" y="580" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- N 标记符 -->
        <mxCell id="13" value="N" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="460" y="380" width="40" height="20" as="geometry" />
        </mxCell>
        
        <!-- N 的括号 -->
        <mxCell id="14" value="" style="endArrow=none;html=1;rounded=0;curved=1;exitX=1;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="280" as="sourcePoint" />
            <mxPoint x="480" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="470" y="280" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="15" value="" style="endArrow=none;html=1;rounded=0;curved=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="280" as="sourcePoint" />
            <mxPoint x="480" y="480" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="16" value="" style="endArrow=none;html=1;rounded=0;curved=1;exitX=1;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="480" as="sourcePoint" />
            <mxPoint x="480" y="480" as="targetPoint" />
            <Array as="points">
              <mxPoint x="470" y="480" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Concat 模块 -->
        <mxCell id="17" value="Concat" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#36393d;" vertex="1" parent="1">
          <mxGeometry x="320" y="580" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 第三个 Bottleneck 到 Concat 的箭头 -->
        <mxCell id="18" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="17">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="730" as="sourcePoint" />
            <mxPoint x="440" y="680" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 最后一个 Conv 模块 -->
        <mxCell id="19" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="320" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Concat 到最后 Conv 的箭头 -->
        <mxCell id="20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="17" target="19">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="830" as="sourcePoint" />
            <mxPoint x="440" y="780" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 最后的输出箭头 -->
        <mxCell id="21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="19">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="830" as="sourcePoint" />
            <mxPoint x="380" y="780" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 绘制四条从各模块左侧连接到Concat的线 -->
        <!-- 从Conv到Concat的直接连接 -->
        <mxCell id="22" value="C*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="17">
          <mxGeometry x="-0.7273" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="110" as="sourcePoint" />
            <mxPoint x="240" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="200" y="110" />
              <mxPoint x="200" y="610" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 从Split到Concat的直接连接 -->
        <mxCell id="23" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="17">
          <mxGeometry x="-0.6364" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="210" as="sourcePoint" />
            <mxPoint x="220" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="210" />
              <mxPoint x="220" y="610" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 从第一个Bottleneck到Concat的直接连接 -->
        <mxCell id="24" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="17">
          <mxGeometry x="-0.5455" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="310" as="sourcePoint" />
            <mxPoint x="240" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="240" y="310" />
              <mxPoint x="240" y="610" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 从第二个Bottleneck到Concat的直接连接 -->
        <mxCell id="25" value="(0.5*C)*H*W" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="17">
          <mxGeometry x="-0.4545" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="410" as="sourcePoint" />
            <mxPoint x="260" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="260" y="410" />
              <mxPoint x="260" y="610" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- 从第三个Bottleneck到Concat的连接由直接箭头表示，已经绘制 -->
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
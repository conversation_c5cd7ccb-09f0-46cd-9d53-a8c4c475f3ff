<mxfile>
    <diagram id="YOLO11_architecture" name="YOLO11 Architecture">
        <mxGraphModel>
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                
                <!-- Backbone -->
                <mxCell id="backbone_title" value="Backbone" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="40" y="20" width="100" height="30" as="geometry"/>
                </mxCell>
                
                <!-- Input -->
                <mxCell id="input" value="Input" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="40" y="60" width="120" height="40" as="geometry"/>
                </mxCell>
                
                <!-- Backbone Layers -->
                <mxCell id="conv1" value="Conv&#10;(64, 3, 2)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="40" y="120" width="120" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="conv2" value="Conv&#10;(128, 3, 2)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="40" y="180" width="120" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="c3k2_1" value="2 × C3k2&#10;(256, False)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
                    <mxGeometry x="40" y="240" width="120" height="40" as="geometry"/>
                </mxCell>
                
                <mxCell id="conv3" value="Conv&#10;(256, 3, 2)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
                    <mxGeometry x="40" y="300" width="120" height="40" as="geometry"/>
                </mxCell>
                
                <!-- Head -->
                <mxCell id="head_title" value="Head" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1" vertex="1" parent="1">
                    <mxGeometry x="280" y="20" width="100" height="30" as="geometry"/>
                </mxCell>
                
                <!-- Detection Outputs -->
                <mxCell id="detect" value="Detect&#10;[P3, P4, P5]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
                    <mxGeometry x="280" y="400" width="120" height="40" as="geometry"/>
                </mxCell>
                
                <!-- Connections -->
                <mxCell id="" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="input" target="conv1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry"/>
                </mxCell>
                
                <!-- Add more connections as needed -->
                
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
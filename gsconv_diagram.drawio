<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-06-24T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="GSConv Architecture" id="gsconv">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <mxCell id="2" value="GSConv Module" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="20" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Main container with dashed border -->
        <mxCell id="3" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;strokeWidth=2;fillColor=none;dashed=1;dashPattern=5 5;" parent="1" vertex="1">
          <mxGeometry x="120" y="80" width="580" height="280" as="geometry" />
        </mxCell>
        
        <!-- Input -->
        <mxCell id="4" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ff6666;strokeColor=#000000;size=10;" parent="1" vertex="1">
          <mxGeometry x="40" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="5" value="input&#xa;C1 channels" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="25" y="230" width="90" height="30" as="geometry" />
        </mxCell>
        
        <!-- Conv branch (top) -->
        <mxCell id="6" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#000000;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="180" y="120" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- C2/2 channels label for Conv -->
        <mxCell id="7" value="C2/2 channels" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="170" y="90" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Conv output (colorful bars) -->
        <mxCell id="8" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ffff99;strokeColor=#000000;size=8;" parent="1" vertex="1">
          <mxGeometry x="300" y="125" width="50" height="30" as="geometry" />
        </mxCell>
        
        <!-- DWConv branch (bottom) -->
        <mxCell id="9" value="DWConv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#99ccff;strokeColor=#000000;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="180" y="240" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- DWConv output (colorful bars) -->
        <mxCell id="10" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#99ff99;strokeColor=#000000;size=8;" parent="1" vertex="1">
          <mxGeometry x="300" y="245" width="50" height="30" as="geometry" />
        </mxCell>
        
        <!-- C2/2 channels label for DWConv -->
        <mxCell id="11" value="C2/2 channels" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="170" y="290" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Concat -->
        <mxCell id="12" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffccff;strokeColor=#000000;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="180" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Concat output (combined colorful bars) -->
        <mxCell id="13" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ccccff;strokeColor=#000000;size=10;" parent="1" vertex="1">
          <mxGeometry x="520" y="185" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- Shuffle -->
        <mxCell id="14" value="shuffle" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ccffcc;strokeColor=#000000;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="180" width="70" height="40" as="geometry" />
        </mxCell>
        
        <!-- Output -->
        <mxCell id="15" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ffcccc;strokeColor=#000000;size=10;" parent="1" vertex="1">
          <mxGeometry x="720" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="16" value="output&#xa;C2 channels" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="705" y="230" width="90" height="30" as="geometry" />
        </mxCell>
        
        <!-- GSConv label -->
        <mxCell id="17" value="GSConv" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=bottom;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#0066cc;" parent="1" vertex="1">
          <mxGeometry x="620" y="320" width="80" height="30" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <!-- Input to Conv -->
        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="190" as="sourcePoint" />
            <mxPoint x="180" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Input to DWConv -->
        <mxCell id="19" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="210" as="sourcePoint" />
            <mxPoint x="180" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Conv to Conv output -->
        <mxCell id="20" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="8">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="140" as="sourcePoint" />
            <mxPoint x="300" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- DWConv to DWConv output -->
        <mxCell id="21" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="260" as="sourcePoint" />
            <mxPoint x="300" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Conv output to Concat -->
        <mxCell id="22" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="8" target="12">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="140" as="sourcePoint" />
            <mxPoint x="400" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- DWConv output to Concat -->
        <mxCell id="23" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="12">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="260" as="sourcePoint" />
            <mxPoint x="400" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Concat to Concat output -->
        <mxCell id="24" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="12" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="200" as="sourcePoint" />
            <mxPoint x="520" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Concat output to Shuffle -->
        <mxCell id="25" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="13" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="200" as="sourcePoint" />
            <mxPoint x="600" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Shuffle to Output -->
        <mxCell id="26" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="14" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="670" y="200" as="sourcePoint" />
            <mxPoint x="720" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

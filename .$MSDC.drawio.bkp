<mxfile host="app.diagrams.net" modified="2023-11-10T12:00:00.000Z" agent="Mozilla/5.0" etag="your-etag" version="21.0.6" type="device">
  <diagram id="MSDC_diagram" name="MSDC">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 外部虚线框 -->
        <mxCell id="2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;dashed=1;strokeColor=#FF00FF;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="200" y="120" width="400" height="320" as="geometry" />
        </mxCell>
        <!-- 标题 -->
        <mxCell id="3" value="(f) MSDC" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="350" y="420" width="100" height="20" as="geometry" />
        </mxCell>
        <!-- DWCpxp -->
        <mxCell id="4" value="DWCpxp" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="220" y="140" width="100" height="40" as="geometry" />
        </mxCell>
        <!-- DWCqxq -->
        <mxCell id="5" value="DWCqxq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="350" y="140" width="100" height="40" as="geometry" />
        </mxCell>
        <!-- DWCsxs -->
        <mxCell id="6" value="DWCsxs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="480" y="140" width="100" height="40" as="geometry" />
        </mxCell>
        <!-- BN 1 -->
        <mxCell id="21" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="220" y="200" width="100" height="30" as="geometry" />
        </mxCell>
        <!-- BN 2 -->
        <mxCell id="22" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="350" y="200" width="100" height="30" as="geometry" />
        </mxCell>
        <!-- BN 3 -->
        <mxCell id="23" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="480" y="200" width="100" height="30" as="geometry" />
        </mxCell>
        <!-- 箭头从DWC到BN -->
        <mxCell id="24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="4" target="21" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="25" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="5" target="22" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="26" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="6" target="23" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- ReLU6 1 -->
        <mxCell id="10" value="ReLU6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="220" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <!-- ReLU6 2 -->
        <mxCell id="11" value="ReLU6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="350" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <!-- ReLU6 3 -->
        <mxCell id="12" value="ReLU6" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="480" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <!-- 箭头从BN到ReLU6 -->
        <mxCell id="27" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="21" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="28" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="22" target="11" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="29" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="23" target="12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 箭头从ReLU6到Channel Shuffle -->
        <mxCell id="13" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="350" y="320" as="targetPoint" />
            <Array as="points">
              <mxPoint x="270" y="300" />
              <mxPoint x="350" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="11" target="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="350" y="320" as="targetPoint" />
            <Array as="points">
              <mxPoint x="530" y="300" />
              <mxPoint x="350" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <!-- Channel Shuffle -->
        <mxCell id="16" value="Channel Shuffle" style="rounded=1;whiteSpace=wrap;html=1;fillColor=white;strokeColor=#FF00FF;" parent="1" vertex="1">
          <mxGeometry x="300" y="320" width="200" height="40" as="geometry" />
        </mxCell>
        <!-- 顶部箭头 -->
        <mxCell id="17" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="120" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="120" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="120" as="sourcePoint" />
            <mxPoint x="450" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 底部箭头 -->
        <mxCell id="20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="400" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
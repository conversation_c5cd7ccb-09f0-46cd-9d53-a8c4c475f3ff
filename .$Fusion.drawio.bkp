<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="YtF2Y89zZtCgW1yT6PqK" name="Page-1">
    <mxGraphModel dx="2058" dy="1161" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Fusion" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="140" y="70" width="560" height="250" as="geometry" />
        </mxCell>
        <mxCell id="3" value="1x1 Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="285" y="100" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4" value="1x1 Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="285" y="240" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="5" value="RepBlock" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff8cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="425" y="240" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="6" value="C" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="175" y="170" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7" value="F" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="630" y="170" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="8" value="⊕" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="550" y="170" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="9" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="120" y="110" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="120" y="230" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="385" y="90" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="385" y="230" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="13" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="505" y="170" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="14" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="590" y="140" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="15" value="N ×" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="505" y="230" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="16" value="C Concatenate" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="175" y="330" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="17" value="⊕ Element-wise add" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="395" y="330" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="18" value="F Flatten" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" vertex="1" parent="1">
          <mxGeometry x="630" y="330" width="70" height="30" as="geometry" />
        </mxCell>
        <!-- 连接线 -->
        <mxCell id="19" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="8" target="7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="20" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="3">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="240" y="190" />
              <mxPoint x="240" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="21" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="4">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="240" y="190" />
              <mxPoint x="240" y="260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="22" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="480" y="120" />
              <mxPoint x="480" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="23" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="24" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="570" y="260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="25" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="6">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="110" y="125" />
              <mxPoint x="110" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="26" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="6">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="110" y="245" />
              <mxPoint x="110" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="27" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="7">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="710" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
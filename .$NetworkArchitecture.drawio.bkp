<mxfile host="app.diagrams.net" modified="2023-09-14T01:29:14.516Z" agent="Mozilla/5.0" etag="lLFbMsKvRtL1TjSYHTlg" version="21.3.8" type="device">
  <diagram name="第 1 页" id="WDkdQHlKTSjrXKCc2Uwk">
    <mxGraphModel dx="1434" dy="790" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Backbone Container -->
        <mxCell id="backbone_container" value="Backbone" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=1 1;align=center;verticalAlign=top;fontStyle=1;fontSize=16;fillColor=none;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="170" height="600" as="geometry" />
        </mxCell>
        
        <!-- Neck Container -->
        <mxCell id="neck_container" value="Neck" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=1 1;align=center;verticalAlign=top;fontStyle=1;fontSize=16;fillColor=none;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="250" y="40" width="350" height="600" as="geometry" />
        </mxCell>
        
        <!-- Head Container -->
        <mxCell id="head_container" value="Head" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=1 1;align=center;verticalAlign=top;fontStyle=1;fontSize=16;fillColor=none;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="640" y="40" width="140" height="600" as="geometry" />
        </mxCell>
        
        <!-- Backbone Components -->
        <mxCell id="conv1" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B3DEFF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="570" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="c3k2_1" value="C3k2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFDEC8;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="520" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv2" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B3DEFF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="470" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="c3k2_2" value="C3k2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFDEC8;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="420" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv3" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B3DEFF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="370" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="c3k2_3" value="C3k2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFDEC8;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="conv4" value="Conv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B3DEFF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="270" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="c3k2_4" value="C3k2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFDEC8;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="220" width="90" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="sppf" value="SPPF" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBB3;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="80" y="170" width="90" height="30" as="geometry" />
        </mxCell>
        
        <!-- Backbone Connections -->
        <mxCell id="backbone_arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv1" target="c3k2_1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_1" target="conv2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv2" target="c3k2_2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_2" target="conv3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv3" target="c3k2_3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_3" target="conv4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow7" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="conv4" target="c3k2_4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backbone_arrow8" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="c3k2_4" target="sppf">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="410" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Backbone Input Label -->
        <mxCell id="backbone_input" value="Linput" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#FF0000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="95" y="610" width="60" height="30" as="geometry" />
        </mxCell>
    
      </root>
    </mxGraphModel>
  </diagram>
</mxfile> 
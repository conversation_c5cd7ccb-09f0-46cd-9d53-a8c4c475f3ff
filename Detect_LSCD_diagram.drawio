<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-06-24T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="Detect_LSCD Architecture" id="detect-lscd-arch">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Main Class Header -->
        <mxCell id="class-header" value="Detect_LSCD (nn.Module)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="40" width="240" height="40" as="geometry" />
        </mxCell>
        
        <!-- Class Variables Section -->
        <mxCell id="class-vars" value="Class Variables&#xa;• dynamic = False&#xa;• export = False&#xa;• shape = None&#xa;• anchors = torch.empty(0)&#xa;• strides = torch.empty(0)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="120" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Constructor Parameters -->
        <mxCell id="init-params" value="__init__ Parameters&#xa;• nc = 80 (classes)&#xa;• hidc = 256 (hidden channels)&#xa;• ch = () (input channels)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="280" y="120" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Instance Variables -->
        <mxCell id="instance-vars" value="Instance Variables&#xa;• nc (number of classes)&#xa;• nl (number of detection layers)&#xa;• reg_max = 16 (DFL channels)&#xa;• no (outputs per anchor)&#xa;• stride (computed strides)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="520" y="120" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Neural Network Components -->
        <mxCell id="conv-layers" value="conv (ModuleList)&#xa;Conv_GN(x, hidc, 3)&#xa;for each input channel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="280" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="share-conv" value="share_conv&#xa;Sequential(&#xa;  Conv_GN(hidc, hidc, 3, g=hidc),&#xa;  Conv_GN(hidc, hidc, 1)&#xa;)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="280" y="280" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="cv2" value="cv2&#xa;Conv2d(hidc, 4*reg_max, 1)&#xa;(Bounding Box Head)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="520" y="280" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="cv3" value="cv3&#xa;Conv2d(hidc, nc, 1)&#xa;(Classification Head)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="720" y="280" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="scale" value="scale (ModuleList)&#xa;Scale(1.0)&#xa;for each input channel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="920" y="280" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="dfl" value="dfl&#xa;DFL(reg_max) if reg_max > 1&#xa;else nn.Identity()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="400" y="400" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- Forward Method Flow -->
        <mxCell id="forward-title" value="Forward Method Flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="500" width="200" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="input-x" value="Input x[i]" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="80" y="540" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="conv-step" value="conv[i](x[i])" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="220" y="540" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="share-conv-step" value="share_conv(x[i])" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="360" y="540" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="concat-step" value="torch.cat(&#xa;scale[i](cv2(x[i])),&#xa;cv3(x[i]))" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="520" y="540" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="training-check" value="Training?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="720" y="540" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="training-output" value="Return x" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="860" y="540" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- Inference Path -->
        <mxCell id="inference-path" value="Inference Path" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="640" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="cat-step" value="x_cat = torch.cat(&#xa;[xi.view(...) for xi in x])" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="80" y="680" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="anchors-step" value="Generate anchors&#xa;and strides" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="280" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="split-step" value="Split box and cls&#xa;from x_cat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="440" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="decode-step" value="dbox = &#xa;decode_bboxes(box)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="600" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="final-output" value="y = torch.cat(&#xa;(dbox, cls.sigmoid()))" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="760" y="680" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Method Boxes -->
        <mxCell id="bias-init" value="bias_init()&#xa;Initialize biases" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="80" y="780" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="decode-bboxes" value="decode_bboxes()&#xa;Decode bounding boxes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="240" y="780" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Connections -->
        <mxCell id="edge1" edge="1" parent="1" source="input-x" target="conv-step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="conv-step" target="share-conv-step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="share-conv-step" target="concat-step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="concat-step" target="training-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="training-check" target="training-output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="training-check" target="cat-step">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="770" y="620" as="sourcePoint" />
            <mxPoint x="160" y="680" as="targetPoint" />
            <Array as="points">
              <mxPoint x="770" y="620" />
              <mxPoint x="160" y="620" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="cat-step" target="anchors-step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="anchors-step" target="split-step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="split-step" target="decode-step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="decode-step" target="final-output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Labels for edges -->
        <mxCell id="training-yes" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="820" y="520" width="40" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="training-no" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="680" y="620" width="40" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

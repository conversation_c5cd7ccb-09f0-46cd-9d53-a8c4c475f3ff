<mxfile host="app.diagrams.net" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" version="24.7.17">
  <diagram name="Page-1" id="page1">
    <mxGraphModel dx="2074" dy="1141" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <mxCell id="2" value="输入特征图 x[i]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="40" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="3" value="Conv_GN 3×3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="230" y="240" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="4" value="共享卷积 (self.share_conv)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="420" y="200" width="360" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="5" value="Conv_GN 3×3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="4">
          <mxGeometry x="20" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="6" value="Conv_GN 1×1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="4">
          <mxGeometry x="200" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="7" value="共享特征" style="rhombus;whiteSpace=wrap;html=1;rounded=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="230" width="120" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="8" value="回归分支 (Regression Branch)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="1000" y="40" width="180" height="230" as="geometry" />
        </mxCell>
        
        <mxCell id="9" value="Conv2d 1×1&lt;br/&gt;(self.cv2)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1;" vertex="1" parent="8">
          <mxGeometry x="20" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="10" value="Scale" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="8">
          <mxGeometry x="20" y="140" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="11" value="&lt;b&gt;pred_corners&lt;/b&gt;&lt;br/&gt;预测框分布" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1230" y="105" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="12" value="分类分支 (Classification Branch)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="1000" y="350" width="180" height="170" as="geometry" />
        </mxCell>
        
        <mxCell id="13" value="Conv2d 1×1&lt;br/&gt;(self.cv3)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3e6;strokeColor=#333333;strokeWidth=2;fontStyle=1" vertex="1" parent="12">
          <mxGeometry x="20" y="50" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="14" value="初始分类得分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="12">
          <mxGeometry x="20" y="120" width="140" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="15" value="位置质量估计 (LQE Module)" style="shape=swimlane;startSize=30;fontSize=12;fontStyle=1;align=center;verticalAlign=top;container=1;collapsible=0;" vertex="1" parent="1">
          <mxGeometry x="1200" y="200" width="410" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="16" value="统计分析&lt;br/&gt;(top-k, mean)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="15">
          <mxGeometry x="20" y="50" width="110" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="17" value="MLP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="15">
          <mxGeometry x="150" y="50" width="110" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="18" value="质量分调整值" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=1;fontStyle=1" vertex="1" parent="15">
          <mxGeometry x="280" y="50" width="110" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="19" value="+" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#b85450;strokeWidth=2;fontSize=24;fontStyle=1;fillColor=#f8cecc;" vertex="1" parent="15">
          <mxGeometry x="185" y="130" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="20" value="&lt;b&gt;pred_scores&lt;/b&gt;&lt;br/&gt;融合后得分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1415" y="420" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="21" value="Concat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1650" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="22" value="输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1820" y="240" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- Connections -->
        <mxCell id="c1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="2" target="3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="3" target="4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="4" source="5" target="6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="4" target="7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="7" target="8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="890" y="155" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="c6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="7" target="12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="890" y="435" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="c7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="8" source="9" target="10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8" target="11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="12" source="13" target="14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="11" target="15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="15" source="16" target="17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="15" source="17" target="18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="15" source="18" target="19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="12" target="19">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1270" y="435" />
              <mxPoint x="1270" y="355" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="c15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="19" target="20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="c16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="11" target="21">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1600" y="135" />
              <mxPoint x="1600" y="255" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="c17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="20" target="21">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1600" y="450" />
              <mxPoint x="1600" y="285" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="c18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="21" target="22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

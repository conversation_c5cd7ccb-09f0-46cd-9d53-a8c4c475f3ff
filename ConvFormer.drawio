<mxfile host="app.diagrams.net" modified="2023-11-10T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="your-etag" version="15.7.3" type="device">
  <diagram id="convformer_block" name="ConvFormer Block">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 主要流程线 -->
        <mxCell id="2" value="" style="endArrow=classic;html=1;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="80" as="sourcePoint" />
            <mxPoint x="200" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 加法节点1 -->
        <mxCell id="3" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="185" y="120" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="+" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="185" y="120" width="30" height="30" as="geometry" />
        </mxCell>
        <!-- 左侧输入到加法节点1 -->
        <mxCell id="5" value="" style="endArrow=classic;html=1;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="140" y="135" as="sourcePoint" />
            <mxPoint x="180" y="135" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Channel MLP 模块 -->
        <mxCell id="6" value="Channel&lt;br&gt;MLP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="160" y="170" width="80" height="50" as="geometry" />
        </mxCell>
        <!-- 加法节点1到Channel MLP的连接 -->
        <mxCell id="7" value="" style="endArrow=classic;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="150" as="sourcePoint" />
            <mxPoint x="200" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Norm 模块1 -->
        <mxCell id="8" value="Norm" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="160" y="240" width="80" height="30" as="geometry" />
        </mxCell>
        <!-- Channel MLP到Norm的连接 -->
        <mxCell id="9" value="" style="endArrow=classic;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="6" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="220" as="sourcePoint" />
            <mxPoint x="200" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 加法节点2 -->
        <mxCell id="10" value="" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="185" y="290" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="+" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="185" y="290" width="30" height="30" as="geometry" />
        </mxCell>
        <!-- Norm到加法节点2的连接 -->
        <mxCell id="12" value="" style="endArrow=classic;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="8" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="270" as="sourcePoint" />
            <mxPoint x="200" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 左侧输入到加法节点2 -->
        <mxCell id="13" value="" style="endArrow=classic;html=1;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="10" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="140" y="305" as="sourcePoint" />
            <mxPoint x="180" y="305" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Separable Convolution 模块 -->
        <mxCell id="14" value="Separable&lt;br&gt;Convolution" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="160" y="340" width="80" height="50" as="geometry" />
        </mxCell>
        <!-- 加法节点2到Separable Convolution的连接 -->
        <mxCell id="15" value="" style="endArrow=classic;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="10" target="14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="320" as="sourcePoint" />
            <mxPoint x="200" y="340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- Norm 模块2 -->
        <mxCell id="16" value="Norm" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="160" y="410" width="80" height="30" as="geometry" />
        </mxCell>
        <!-- Separable Convolution到Norm的连接 -->
        <mxCell id="17" value="" style="endArrow=classic;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="14" target="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="390" as="sourcePoint" />
            <mxPoint x="200" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 输出箭头 -->
        <mxCell id="18" value="" style="endArrow=classic;html=1;strokeWidth=2;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="440" as="sourcePoint" />
            <mxPoint x="200" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <!-- 标签 -->
        <mxCell id="19" value="(g) ConvFormer&lt;br&gt;block" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="150" y="460" width="100" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
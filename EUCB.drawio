<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="EUCB_diagram" name="EUCB">
    <mxGraphModel dx="2314" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;fillColor=none;strokeWidth=2;strokeColor=#99CC99;" parent="1" vertex="1">
          <mxGeometry x="120" y="120" width="640" height="160" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Up_2x" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="160" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4" value="DWC3x3" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="280" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="5" value="BN" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="400" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6" value="ReLU" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="520" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Conv1x1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;strokeColor=#99CC99;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="640" y="160" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="8" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="3" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="9" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="4" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="10" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="5" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="11" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="12" value="" style="endArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="200" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="13" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="760" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="EUCB" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="400" y="260" width="80" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="neural_network_blocks" name="Neural Network Architecture">
    <mxGraphModel dx="1928" dy="997" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="top_container" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;strokeWidth=1;fillColor=none;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="80" y="80" width="680" height="120" as="geometry" />
        </mxCell>
        <mxCell id="c3_label" value="C3" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="710" y="90" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="x_in_top" value="x&lt;sub&gt;in&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="70" y="120" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="conv1_top1" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="140" y="90" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="conv1_top2" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="140" y="150" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bottleneck_top" value="Bottleneck" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#00CC99;strokeColor=#006666;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="310" y="150" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="c_node_top" value="C" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="520" y="125" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv2_top" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="600" y="120" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="x_out_top" value="x&lt;sub&gt;out&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="730" y="130" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="xn_top" value="×n" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="405" y="150" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="edge_xin_conv1_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="x_in_top" target="conv1_top1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="130" as="sourcePoint" />
            <mxPoint x="160" y="80" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="130" />
              <mxPoint x="120" y="110" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_xin_conv2_top" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="x_in_top" target="conv1_top2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="130" as="sourcePoint" />
            <mxPoint x="160" y="180" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="130" />
              <mxPoint x="120" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_conv1_top1_c" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="conv1_top1" target="c_node_top" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="110" as="sourcePoint" />
            <mxPoint x="400" y="110" as="targetPoint" />
            <Array as="points">
              <mxPoint x="490" y="110" />
              <mxPoint x="490" y="140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_conv1_top2_bottleneck" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="conv1_top2" target="bottleneck_top" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="250" y="170" as="sourcePoint" />
            <mxPoint x="300" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_bottleneck_c" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="bottleneck_top" target="c_node_top" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="170" as="sourcePoint" />
            <mxPoint x="470" y="120" as="targetPoint" />
            <Array as="points">
              <mxPoint x="490" y="170" />
              <mxPoint x="490" y="140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_c_conv2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="c_node_top" target="conv2_top" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="140" as="sourcePoint" />
            <mxPoint x="600" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_conv2_xout" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="conv2_top" target="x_out_top" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="140" as="sourcePoint" />
            <mxPoint x="750" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bottom_container" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;strokeWidth=1;fillColor=none;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="80" y="240" width="680" height="120" as="geometry" />
        </mxCell>
        <mxCell id="c3k_label" value="C3k" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="710" y="250" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="x_in_bottom" value="x&lt;sub&gt;in&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="80" y="280" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="conv1_bottom1" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="140" y="250" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="conv1_bottom2" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="140" y="310" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bottleneck_bottom" value="Bottleneck" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#006666;strokeColor=#006666;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="310" y="310" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="c_node_bottom" value="C" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="520" y="285" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv2_bottom" value="Conv(k=1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4D7AFF;strokeColor=#001DBC;fontColor=#ffffff;" parent="1" vertex="1">
          <mxGeometry x="600" y="280" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="x_out_bottom" value="x&lt;sub&gt;out&lt;/sub&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="730" y="290" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="xn_bottom" value="×2" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="405" y="310" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="edge_xin_conv1_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="x_in_bottom" target="conv1_bottom1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="290" as="sourcePoint" />
            <mxPoint x="160" y="240" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="290" />
              <mxPoint x="120" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_xin_conv2_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="x_in_bottom" target="conv1_bottom2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="110" y="290" as="sourcePoint" />
            <mxPoint x="160" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="290" />
              <mxPoint x="120" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_conv1_bottom1_c" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="conv1_bottom1" target="c_node_bottom" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="270" as="sourcePoint" />
            <mxPoint x="400" y="270" as="targetPoint" />
            <Array as="points">
              <mxPoint x="490" y="270" />
              <mxPoint x="490" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_conv1_bottom2_bottleneck" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="conv1_bottom2" target="bottleneck_bottom" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="250" y="330" as="sourcePoint" />
            <mxPoint x="300" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_bottleneck_c_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="bottleneck_bottom" target="c_node_bottom" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="330" as="sourcePoint" />
            <mxPoint x="470" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="490" y="330" />
              <mxPoint x="490" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_c_conv2_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="c_node_bottom" target="conv2_bottom" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="550" y="300" as="sourcePoint" />
            <mxPoint x="600" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_conv2_xout_bottom" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="conv2_bottom" target="x_out_bottom" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="300" as="sourcePoint" />
            <mxPoint x="750" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

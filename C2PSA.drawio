<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="some_id" name="Page-1">
    <mxGraphModel dx="1176" dy="663" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="c2psa_container" value="C2PSA" style="rounded=1;dashed=1;strokeColor=#000000;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="50" y="50" width="200" height="250" as="geometry" />
        </mxCell>
        <mxCell id="conv1_c2psa" value="Conv" style="rounded=1;fillColor=#ffe699;strokeColor=#b38500;" parent="c2psa_container" vertex="1">
          <mxGeometry x="50" y="20" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="psablock1_c2psa" value="PSABlock" style="rounded=1;fillColor=#a20025;strokeColor=#6c0018;fontColor=#ffffff;" parent="c2psa_container" vertex="1">
          <mxGeometry x="50" y="70" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ellipsis_c2psa" value="..." style="text;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;" parent="c2psa_container" vertex="1">
          <mxGeometry x="85" y="110" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="psablockn_c2psa" value="PSABlock" style="rounded=1;fillColor=#a20025;strokeColor=#6c0018;fontColor=#ffffff;" parent="c2psa_container" vertex="1">
          <mxGeometry x="50" y="140" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="concat_c2psa" value="Concat" style="rounded=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="c2psa_container" vertex="1">
          <mxGeometry x="50" y="190" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv2_c2psa" value="Conv" style="rounded=1;fillColor=#ffe699;strokeColor=#b38500;" parent="c2psa_container" vertex="1">
          <mxGeometry x="50" y="230" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="arrow1_c2psa" parent="c2psa_container" source="conv1_c2psa" target="psablock1_c2psa" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow2_c2psa" parent="c2psa_container" source="psablock1_c2psa" target="ellipsis_c2psa" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow3_c2psa" parent="c2psa_container" source="ellipsis_c2psa" target="psablockn_c2psa" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow4_c2psa" parent="c2psa_container" source="psablockn_c2psa" target="concat_c2psa" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow5_c2psa" parent="c2psa_container" source="concat_c2psa" target="conv2_c2psa" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow_loop_c2psa" value="N" style="rounded=0;dashed=1;exitX=1;exitY=0.5;entryX=1;entryY=0.5;" parent="c2psa_container" source="psablock1_c2psa" target="psablockn_c2psa" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="85" as="targetPoint" />
            <mxPoint x="210" y="155" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="psablock_container" value="PSABlock" style="rounded=1;dashed=1;strokeColor=#000000;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="350" y="50" width="200" height="250" as="geometry" />
        </mxCell>
        <mxCell id="input_psablock" style="shape=ellipse;perimeter=ellipsePerimeter;html=1;dashed=1;" parent="psablock_container" vertex="1">
          <mxGeometry x="80" y="20" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="attention_psablock" value="Attention" style="rounded=1;fillColor=#648fff;strokeColor=#0051cc;fontColor=#ffffff;" parent="psablock_container" vertex="1">
          <mxGeometry x="50" y="70" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv1_psablock" value="Conv" style="rounded=1;fillColor=#ffe699;strokeColor=#b38500;" parent="psablock_container" vertex="1">
          <mxGeometry x="50" y="140" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="conv2_psablock" value="Conv" style="rounded=1;fillColor=#ffe699;strokeColor=#b38500;" parent="psablock_container" vertex="1">
          <mxGeometry x="50" y="190" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="output_psablock" style="shape=ellipse;perimeter=ellipsePerimeter;html=1;dashed=1;" parent="psablock_container" vertex="1">
          <mxGeometry x="80" y="230" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="arrow_in_psablock" parent="psablock_container" source="input_psablock" target="attention_psablock" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow_att_conv1" parent="psablock_container" source="attention_psablock" target="conv1_psablock" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow_conv1_conv2" parent="psablock_container" source="conv1_psablock" target="conv2_psablock" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow_conv2_out" parent="psablock_container" source="conv2_psablock" target="output_psablock" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="shortcut1_psablock" value="Shortcut" style="text;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;dashed=1;" parent="psablock_container" vertex="1">
          <mxGeometry x="160" y="75" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="shortcut2_psablock" value="Shortcut" style="text;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;dashed=1;" parent="psablock_container" vertex="1">
          <mxGeometry x="160" y="145" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="arrow_shortcut1_add1" style="dashed=1;" parent="psablock_container" source="input_psablock" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="99.57142857142867" y="115" as="targetPoint" />
            <mxPoint x="140" y="60" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow_att_add1" parent="psablock_container" source="attention_psablock" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="99.6470588235295" y="115" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow_conv1_add2" parent="psablock_container" source="conv1_psablock" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="99.72727272727275" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow_shortcut2_add2" style="dashed=1;" parent="psablock_container" source="input_psablock" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="99.54385964912262" y="170" as="targetPoint" />
            <mxPoint x="130" y="60" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

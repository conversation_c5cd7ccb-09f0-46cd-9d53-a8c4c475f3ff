<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram name="第 1 页" id="page-1">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="X" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="360" y="40" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="320" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontFamily=Times New Roman;" parent="1" source="2" target="3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="60" as="sourcePoint" />
            <mxPoint x="430" y="10" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5" value="Split" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="320" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;" parent="1" source="3" target="5" edge="1">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7" value="MSCB" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="320" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;" parent="1" source="5" target="7" edge="1">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="10" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;fontFamily=Times New Roman;" parent="1" source="7" edge="1">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="530" as="sourcePoint" />
            <mxPoint x="380" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zGWbMIw9v6kQadutj31b-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;endArrow=none;endFill=0;fontFamily=Times New Roman;" edge="1" parent="1" source="11">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="260" y="510" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="11" value="MSCB" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="320" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="17" value="Concat" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffcc99;strokeColor=#36393d;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="320" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;" parent="1" source="11" target="17" edge="1">
          <mxGeometry x="0.1667" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="630" as="sourcePoint" />
            <mxPoint x="440" y="580" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontFamily=Times New Roman;" parent="1" vertex="1">
          <mxGeometry x="320" y="580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Times New Roman;" parent="1" source="17" target="19" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="730" as="sourcePoint" />
            <mxPoint x="440" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontFamily=Times New Roman;" parent="1" source="19" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="730" as="sourcePoint" />
            <mxPoint x="380" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;" parent="1" source="5" target="17" edge="1">
          <mxGeometry x="-0.6364" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="210" as="sourcePoint" />
            <mxPoint x="220" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="210" />
              <mxPoint x="220" y="510" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Times New Roman;" parent="1" source="7" target="17" edge="1">
          <mxGeometry x="-0.5455" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="320" y="310" as="sourcePoint" />
            <mxPoint x="240" y="610" as="targetPoint" />
            <Array as="points">
              <mxPoint x="240" y="310" />
              <mxPoint x="240" y="510" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zGWbMIw9v6kQadutj31b-27" value="" style="labelPosition=right;align=left;strokeWidth=1;shape=mxgraph.mockup.markup.curlyBrace;html=1;shadow=0;dashed=0;strokeColor=#000000;direction=south;fontColor=default;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="440" y="310" width="20" height="100" as="geometry" />
        </mxCell>
        <mxCell id="zGWbMIw9v6kQadutj31b-28" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="455" y="345" width="30" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

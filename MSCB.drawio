<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="MSCB_diagram" name="MSCB">
    <mxGraphModel dx="1975" dy="1139" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;strokeWidth=2;fillColor=none;strokeColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="120" y="110" width="550" height="170" as="geometry" />
        </mxCell>
        <mxCell id="3" value="MSCB" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="360" y="250" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Conv1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontSize=12;verticalAlign=middle;align=center;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="160" y="160" width="70" height="80" as="geometry" />
        </mxCell>
        <mxCell id="5" value="BN,SiLU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;verticalAlign=middle;align=center;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="260" y="160" width="50" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6" value="MSDC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;verticalAlign=middle;align=center;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="340" y="160" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Conv1x1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontSize=12;verticalAlign=middle;align=center;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="430" y="160" width="70" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AsERiU21k0m5W63FvgZR-24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;" edge="1" parent="1" source="8" target="AsERiU21k0m5W63FvgZR-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8" value="BN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;verticalAlign=middle;align=center;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="530" y="160" width="50" height="80" as="geometry" />
        </mxCell>
        <mxCell id="12" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="130" y="200" as="sourcePoint" />
            <mxPoint x="160" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="13" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="4" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="230" y="200" as="sourcePoint" />
            <mxPoint x="260" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="5" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="310" y="200" as="sourcePoint" />
            <mxPoint x="340" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="15" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="6" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="200" as="sourcePoint" />
            <mxPoint x="430" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="16" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="7" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="200" as="sourcePoint" />
            <mxPoint x="530" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;edgeStyle=orthogonalEdgeStyle;entryX=0.458;entryY=-0.023;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1" target="AsERiU21k0m5W63FvgZR-22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="140" y="200" as="sourcePoint" />
            <mxPoint x="600" y="99.9" as="targetPoint" />
            <Array as="points">
              <mxPoint x="140" y="130" />
              <mxPoint x="638" y="130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="AsERiU21k0m5W63FvgZR-23" value="" style="group;strokeWidth=1;" vertex="1" connectable="0" parent="1">
          <mxGeometry x="620" y="180" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AsERiU21k0m5W63FvgZR-19" value="&lt;div style=&quot;text-align: justify;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fontSize=51;fontFamily=Times New Roman;strokeWidth=1;fillColor=default;strokeColor=#6c8ebf;align=center;" vertex="1" parent="AsERiU21k0m5W63FvgZR-23">
          <mxGeometry x="2.5" y="2.5" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="AsERiU21k0m5W63FvgZR-22" value="&lt;font style=&quot;font-size: 20px;&quot;&gt;+&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;strokeWidth=1;" vertex="1" parent="AsERiU21k0m5W63FvgZR-23">
          <mxGeometry width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

<mxfile host="app.diagrams.net" modified="2023-11-15T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="your-etag" version="15.4.3" type="device">
  <diagram id="bifpn-diagram" name="BiFPN Structure">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- P5-32 路径 -->
        <mxCell id="p5-32" value="P5-32" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p5-conv" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="160" y="40" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p5-upsample" value="Upsample" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EA6B66;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="280" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p5-fusion" value="Fusion" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#67AB9F;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1000" y="40" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p5-block" value="Block" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6C8EBF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1120" y="40" width="60" height="40" as="geometry" />
        </mxCell>
        
        <!-- P4-16 路径 -->
        <mxCell id="p4-16" value="P4-16" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="40" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-conv" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="160" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-fusion1" value="Fusion" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#67AB9F;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="280" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-block" value="Block" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6C8EBF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="400" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-upsample" value="Upsample" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#EA6B66;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="520" y="180" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-fusion2" value="Fusion" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#67AB9F;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="880" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-block2" value="Block" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6C8EBF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1000" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-conv2" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1120" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p4-detect" value="Detect" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#80CBC4;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1240" y="180" width="60" height="40" as="geometry" />
        </mxCell>
        
        <!-- P3-8 路径 -->
        <mxCell id="p3-8" value="P3-8" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="40" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p3-conv" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="160" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p3-fusion1" value="Fusion" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#67AB9F;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="520" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p3-block1" value="Block" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6C8EBF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="640" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p3-fusion2" value="Fusion" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#67AB9F;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="760" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p3-block2" value="Block" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6C8EBF;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="880" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p3-conv2" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1000" y="320" width="60" height="40" as="geometry" />
        </mxCell>
        
        <!-- P2-4 路径 -->
        <mxCell id="p2-4" value="P2-4" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="40" y="460" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="p2-conv" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="640" y="460" width="60" height="40" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- P5 连接 -->
        <mxCell id="p5-conn1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p5-32" target="p5-conv">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p5-conn2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p5-conv" target="p5-upsample">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p5-conn3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p5-fusion" target="p5-block">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- P4 连接 -->
        <mxCell id="p4-conn1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-16" target="p4-conv">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p4-conn2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-conv" target="p4-fusion1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p4-conn3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-fusion1" target="p4-block">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p4-conn4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-block" target="p4-upsample">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p4-conn5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-fusion2" target="p4-block2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p4-conn6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-block2" target="p4-conv2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p4-conn7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-conv2" target="p4-detect">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- P3 连接 -->
        <mxCell id="p3-conn1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-8" target="p3-conv">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p3-conn2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-fusion1" target="p3-block1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p3-conn3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-block1" target="p3-fusion2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p3-conn4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-fusion2" target="p3-block2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="p3-conn5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-block2" target="p3-conv2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- P2 连接 -->
        <mxCell id="p2-conn1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p2-4" target="p2-conv">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="400" y="480"/>
              <mxPoint x="400" y="480"/>
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 跨层级连接 -->
        <mxCell id="cross-conn1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="p5-conv" target="p4-fusion1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="190" y="120"/>
              <mxPoint x="310" y="120"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cross-conn2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="p5-upsample" target="p4-fusion1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="cross-conn3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-block" target="p3-fusion1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="430" y="260"/>
              <mxPoint x="550" y="260"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cross-conn4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-upsample" target="p3-fusion1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="cross-conn5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-conv" target="p3-fusion1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="190" y="400"/>
              <mxPoint x="550" y="400"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cross-conn6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="p2-conv" target="p3-fusion2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="670" y="400"/>
              <mxPoint x="790" y="400"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cross-conn7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="p3-conv2" target="p4-fusion2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1030" y="260"/>
              <mxPoint x="910" y="260"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cross-conn8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="p4-conv2" target="p5-fusion">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1150" y="120"/>
              <mxPoint x="1030" y="120"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cross-conn9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="p5-block" target="p4-detect">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1200" y="60"/>
              <mxPoint x="1200" y="200"/>
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

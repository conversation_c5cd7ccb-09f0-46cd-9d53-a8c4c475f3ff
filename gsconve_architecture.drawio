<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-06-24T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17" type="device">
  <diagram name="GSConvE Architecture" id="gsconve">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <mxCell id="2" value="GSConvE Module Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="20" width="250" height="30" as="geometry" />
        </mxCell>

        <mxCell id="3" value="Input x&#xa;[B, c1, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="350" y="80" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="4" value="cv1: Conv&#xa;(c1 → c_=c2//2)&#xa;k=1, s=1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="200" y="180" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="5" value="x1&#xa;[B, c_, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="200" y="280" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="6" value="cv2: Sequential" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="480" y="160" width="120" height="20" as="geometry" />
        </mxCell>

        <mxCell id="7" value="Conv2d&#xa;(c_ → c_)&#xa;3x3, stride=1, pad=1&#xa;bias=False" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="480" y="190" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="8" value="Conv2d&#xa;(c_ → c_)&#xa;3x3, stride=1, pad=1&#xa;groups=c_, bias=False" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" parent="1" vertex="1">
          <mxGeometry x="480" y="270" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="9" value="GELU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="480" y="350" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="10" value="x2&#xa;[B, c_, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="480" y="400" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="11" value="torch.cat((x1, x2), dim=1)&#xa;[B, c2, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="480" width="180" height="50" as="geometry" />
        </mxCell>

        <mxCell id="12" value="y.reshape&#xa;[B, 2, c2//2, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="320" y="560" width="180" height="40" as="geometry" />
        </mxCell>

        <mxCell id="13" value="y.permute(0, 2, 1, 3, 4)&#xa;Channel Shuffle" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="620" width="180" height="40" as="geometry" />
        </mxCell>

        <mxCell id="14" value="y.reshape&#xa;[B, c2, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="320" y="680" width="180" height="40" as="geometry" />
        </mxCell>

        <mxCell id="15" value="Output&#xa;[B, c2, H, W]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="350" y="750" width="120" height="50" as="geometry" />
        </mxCell>

        <mxCell id="16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="130" as="sourcePoint" />
            <mxPoint x="260" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="17" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="130" as="sourcePoint" />
            <mxPoint x="540" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="18" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="240" as="sourcePoint" />
            <mxPoint x="260" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="19" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="7" target="8">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="250" as="sourcePoint" />
            <mxPoint x="540" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="330" as="sourcePoint" />
            <mxPoint x="540" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="21" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="380" as="sourcePoint" />
            <mxPoint x="540" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="320" as="sourcePoint" />
            <mxPoint x="320" y="505" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="23" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="440" as="sourcePoint" />
            <mxPoint x="500" y="505" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="24" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="12">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="530" as="sourcePoint" />
            <mxPoint x="410" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="25" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="12" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="600" as="sourcePoint" />
            <mxPoint x="410" y="620" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="26" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="13" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="660" as="sourcePoint" />
            <mxPoint x="410" y="680" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="27" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="14" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="720" as="sourcePoint" />
            <mxPoint x="410" y="750" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="28" value="c_ = c2 // 2" style="text;html=1;strokeColor=none;fillColor=#f0f0f0;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="50" y="200" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="29" value="Depthwise Conv" style="text;html=1;strokeColor=none;fillColor=#f0f0f0;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="620" y="290" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="30" value="Channel Shuffle&#xa;for better feature mixing" style="text;html=1;strokeColor=none;fillColor=#f0f0f0;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="520" y="630" width="120" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

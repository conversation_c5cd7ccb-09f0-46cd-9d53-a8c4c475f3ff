<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="YtF2Y89zZtCgW1yT6PqK" name="Page-1">
    <mxGraphModel dx="830" dy="478" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Fusion" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2e6;strokeColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="170" y="80" width="530" height="200" as="geometry" />
        </mxCell>
        <mxCell id="3" value="&lt;font face=&quot;Times New Roman&quot;&gt;1x1 Conv&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="305" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4" value="&lt;font face=&quot;Times New Roman&quot;&gt;1x1 Conv&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="305" y="230" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="5" value="&lt;font face=&quot;Times New Roman&quot;&gt;RepBlock&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff8cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="445" y="230" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="6" value="&lt;font face=&quot;Times New Roman&quot;&gt;C&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="195" y="160" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7" value="&lt;font face=&quot;Times New Roman&quot;&gt;F&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" parent="1" vertex="1">
          <mxGeometry x="650" y="160" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="8" value="+" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;fontSize=24;" parent="1" vertex="1">
          <mxGeometry x="570" y="160" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="9" value="&lt;font face=&quot;Times New Roman&quot;&gt;c&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="140" y="100" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="&lt;font face=&quot;Times New Roman&quot;&gt;c&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="140" y="220" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="&lt;font face=&quot;Times New Roman&quot;&gt;c&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="405" y="80" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="&lt;font face=&quot;Times New Roman&quot;&gt;c&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="405" y="220" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="13" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="525" y="160" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="14" value="c" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="610" y="130" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="15" value="&lt;font face=&quot;Times New Roman&quot;&gt;N ×&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="470" y="200" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="16" value="&lt;font face=&quot;Times New Roman&quot;&gt;Concatenate&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="210" y="305" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="17" value="&lt;font face=&quot;Times New Roman&quot;&gt;&amp;nbsp;Element-wise add&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="390" y="305" width="125" height="30" as="geometry" />
        </mxCell>
        <mxCell id="18" value="&lt;font face=&quot;Times New Roman&quot;&gt;&amp;nbsp;Flatten&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="630" y="305" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="8" target="7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="20" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6" target="3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="260" y="180" />
              <mxPoint x="260" y="110" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="21" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6" target="4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="260" y="180" />
              <mxPoint x="260" y="250" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="22" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3" target="8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="110" />
              <mxPoint x="590" y="110" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="23" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="4" target="5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="24" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="5" target="8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="250" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="27" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="730" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="YjvNhSIH9HaWR4goctAD-28" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" target="6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="130" as="sourcePoint" />
            <mxPoint x="150" y="140" as="targetPoint" />
            <Array as="points">
              <mxPoint x="215" y="130" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="YjvNhSIH9HaWR4goctAD-29" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" target="6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="230" as="sourcePoint" />
            <mxPoint x="170" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="215" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="YjvNhSIH9HaWR4goctAD-30" value="&lt;font face=&quot;Times New Roman&quot;&gt;C&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="170" y="300" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="YjvNhSIH9HaWR4goctAD-32" value="&lt;font face=&quot;Times New Roman&quot;&gt;F&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="590" y="300" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="YjvNhSIH9HaWR4goctAD-33" value="+" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffffff;strokeColor=#000000;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="350" y="300" width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-06-24T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="Transformer Encoder" id="transformer-encoder">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="Transformer Encoder" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="40" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- Main Container -->
        <mxCell id="container" value="" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#f5f5f5;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="80" width="300" height="520" as="geometry" />
        </mxCell>
        
        <!-- L x label -->
        <mxCell id="lx_label" value="L x" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="100" width="30" height="20" as="geometry" />
        </mxCell>
        
        <!-- Top Add Circle -->
        <mxCell id="add_top" value="+" style="ellipse;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#ffffff;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="130" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- MLP Block -->
        <mxCell id="mlp" value="MLP" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#b3d9ff;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="200" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- Norm Block 1 -->
        <mxCell id="norm1" value="Norm" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#fff2cc;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="270" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- Bottom Add Circle -->
        <mxCell id="add_bottom" value="+" style="ellipse;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#ffffff;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="340" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- Multi-Head Attention Block -->
        <mxCell id="attention" value="Multi-Head&#xa;Attention" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#d5e8d4;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="410" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Norm Block 2 -->
        <mxCell id="norm2" value="Norm" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#fff2cc;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="490" width="120" height="40" as="geometry" />
        </mxCell>
        
        <!-- Embedded Patches -->
        <mxCell id="embedded" value="Embedded&#xa;Patches" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#000000;fillColor=#f8cecc;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="620" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <!-- From Embedded to Norm2 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="620" as="sourcePoint" />
            <mxPoint x="380" y="530" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- From Norm2 to Attention -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="490" as="sourcePoint" />
            <mxPoint x="380" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- From Attention to bottom Add -->
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="410" as="sourcePoint" />
            <mxPoint x="380" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- From bottom Add to Norm1 -->
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="355" as="sourcePoint" />
            <mxPoint x="440" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- From Norm1 to MLP -->
        <mxCell id="arrow5" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="270" as="sourcePoint" />
            <mxPoint x="380" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- From MLP to top Add -->
        <mxCell id="arrow6" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="200" as="sourcePoint" />
            <mxPoint x="380" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Skip connection for bottom Add -->
        <mxCell id="skip1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;curved=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="520" as="sourcePoint" />
            <mxPoint x="510" y="355" as="targetPoint" />
            <Array as="points">
              <mxPoint x="560" y="520" />
              <mxPoint x="560" y="355" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Skip connection for top Add -->
        <mxCell id="skip2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;curved=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="310" as="sourcePoint" />
            <mxPoint x="510" y="145" as="targetPoint" />
            <Array as="points">
              <mxPoint x="580" y="310" />
              <mxPoint x="580" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Three arrows into Multi-Head Attention -->
        <mxCell id="arrow_att1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="350" y="490" as="sourcePoint" />
            <mxPoint x="350" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow_att2" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="410" y="490" as="sourcePoint" />
            <mxPoint x="410" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

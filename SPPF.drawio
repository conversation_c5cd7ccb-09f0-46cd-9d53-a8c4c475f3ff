<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5">
  <diagram id="YtF2Y89zZtCgW1yT6PqK" name="Page-1">
    <mxGraphModel dx="2058" dy="1161" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="SPPF" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6e6;strokeColor=#cc0000;dashed=1;fontColor=#cc0000;" parent="1" vertex="1">
          <mxGeometry x="135" y="70" width="725" height="80" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="160" y="90" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="MaxPool" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="280" y="90" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="MaxPool" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="400" y="90" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6" value="MaxPool" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="520" y="90" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Concat" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="640" y="90" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Conv" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4e3f7;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="760" y="90" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="3" target="4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="10" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="4" target="5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="680" y="90" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="11" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="5" target="6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="12" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6" target="7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="15" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="7" target="8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="17" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="3" target="3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="150" y="105" as="targetPoint" />
            <mxPoint x="200" y="120" as="sourcePoint" />
            <mxArray as="points">
              <mxPoint x="130" y="105" />
              <mxPoint x="130" y="160" />
              <mxPoint x="200" y="160" />
            </mxArray>
          </mxGeometry>
        </mxCell>
        <mxCell id="18" style="edgeStyle=orthogonalEdge;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="8" target="8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="810" y="105" as="targetPoint" />
            <mxPoint x="760" y="120" as="sourcePoint" />
            <mxArray as="points">
              <mxPoint x="840" y="105" />
              <mxPoint x="840" y="160" />
              <mxPoint x="760" y="160" />
            </mxArray>
          </mxGeometry>
        </mxCell>
        <mxCell id="Xjc4Ae5-Ruw1G5XhOhd_-18" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="105" as="sourcePoint" />
            <mxPoint x="310" y="50" as="targetPoint" />
            <Array as="points">
              <mxPoint x="260" y="80" />
              <mxPoint x="680" y="70" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
